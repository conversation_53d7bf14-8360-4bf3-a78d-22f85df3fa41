//
//  PaymentPlanViewController.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 12/08/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import UIKit

final class PaymentPlanViewController: UIViewController {
    
    //Default is 60. This value gets updated while rendering the bottom view
    private var bottomViewHeight : CGFloat = 60
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: view.bounds, style: .plain)
        tableView.showsVerticalScrollIndicator = false
        tableView.showsHorizontalScrollIndicator = false
        tableView.backgroundColor = .background
        tableView.separatorStyle = .none
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 140.0
        tableView.sectionHeaderHeight = UITableView.automaticDimension
        tableView.estimatedSectionHeaderHeight = 20
        tableView.tableFooterView = UIView(frame: .init(x: 0, y: 0, width: 0, height: 80))
        return tableView
    }()
    
    private lazy var priceLbl: UILabel = {
        let lbl = UILabel()
        lbl.font = UIFont(.regular, size: 20)
        lbl.textColor = .white
        lbl.prepareForAutolayout()
        return lbl
    }()
    
    private lazy var discountLbl: UILabel = {
        let lbl = UILabel()
        lbl.font = UIFont(.regular, size: 12)
        lbl.textColor = .brownGrey
        lbl.prepareForAutolayout()
        return lbl
    }()
    
    private lazy var continueBtn: GradientButton = {
        let button = GradientButton()
        button.translatesAutoresizingMaskIntoConstraints = false
        button.contentEdgeInsets.left = 32
        button.contentEdgeInsets.right = 32
        button.setTitle(Strings.Button.continueText, for: .normal)
        button.prepareForAutolayout()
        return button
    }()
    
    private lazy var bottomView: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: 200, height: 200))
        view.backgroundColor = .darkGreyBlue
        view.prepareForAutolayout()
        return view
    }()
    
    private lazy var skipBarButton: UIBarButtonItem = {
        let barButtonItem = UIBarButtonItem(title: Strings.Button.skip, style: .plain, target: self, action: #selector(skipBarButtonTapped))
        barButtonItem.setTitleTextAttributesForAllStates()
        return barButtonItem
    }()
    
    private lazy var carouselRect: CGRect = {
        let width = tableView.bounds.width
        //Height of carousel cards: tableView.bounds.width * 0.8 * 0.25 (aspect ratio)
        //Vertial padding for carousel: 16+16
        let height = tableView.bounds.width * 0.8 * 0.25 + 32
        return CGRect(x: 0, y: 0, width: width, height: height)
    }()
    
    var viewModel: PlanViewModel?
    
    //MARK: Properties
    var presenter: PaymentPlanViewOutput?
    
    //MARK: Initialization
    override init(nibName nibNameOrNil: String? = nil, bundle nibBundleOrNil: Bundle? = nil) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //MARK: ViewController Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupViews()
        setupConstraints()
        themeViews()
        presenter?.viewDidLoad()
        presenter?.fetchPlans()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.refreshTnCMessages()
        conditionalThemifyViews()
        AnalyticsManager.shared.trackScreen(withName: .planDisplayScreen)
    }
    
    //MARK: Private Methods
    
    //Configure Views and subviews
    private func setupViews() {
        view.addSubview(tableView)
        setupTableView()
        view.addSubview(bottomView)
        continueBtn.addTarget(self, action: #selector(continueAction(_:)), for: .touchUpInside)
    }
    
    //Apply Theming for views here
    private func themeViews() {
        view.backgroundColor = .background
        removeBackButtonText()
        hideNavigationBar(false)
        themeNavigationBar(style: .transparent)
        self.title = "Choose Your Plan"
        showBottomView(false, animated: false)
    }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    //Apply conditional Theming for views here
    private func conditionalThemifyViews() {
        themifyCTA()
    }
    
    //Themify CTA
    private func themifyCTA() {
        if let route = presenter?.userRoute {
            switch route {
            case .directlyPay:
                continueBtn.setTitle(Strings.Button.continueText, for: .normal)
                
            case .loginAndPay:
                continueBtn.setTitle(Strings.Button.continueText, for: .normal)
            }
        }
    }
    
    //Apply AutoLayout Constraints
    private func setupConstraints() {
        
        setupTableviewConstraints()
        setupBottomViewConstraints()
    }
    
    private func setupTableviewConstraints() {
        //Tableview constraints
        tableView.prepareForAutolayout()
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupBottomViewConstraints() {
        
        //Bottom Price View
        bottomView.addSubview(priceLbl)
        bottomView.addSubview(discountLbl)
        
        NSLayoutConstraint.activate([
            priceLbl.leadingAnchor.constraint(equalTo: bottomView.leadingAnchor, constant: 16),
            priceLbl.topAnchor.constraint(equalTo: bottomView.topAnchor, constant: 16),
            discountLbl.leadingAnchor.constraint(equalTo: priceLbl.trailingAnchor, constant: 8),
            discountLbl.centerYAnchor.constraint(equalTo: priceLbl.centerYAnchor)
        ])
        
        bottomView.addSubview(continueBtn)
        
        //CTA constraints
        NSLayoutConstraint.activate([
            continueBtn.topAnchor.constraint(equalTo: bottomView.topAnchor, constant: 6),
            continueBtn.trailingAnchor.constraint(equalTo: bottomView.trailingAnchor, constant: -16),
            continueBtn.heightAnchor.constraint(equalToConstant: 48)
        ])
        
        //Update the Bottom View height required for animation later
        bottomViewHeight = 48 + 6 + 6
        
        //Bottom View constraints
        NSLayoutConstraint.activate([
            bottomView.leadingAnchor.constraint(equalTo: tableView.leadingAnchor),
            bottomView.trailingAnchor.constraint(equalTo: tableView.trailingAnchor),
            bottomView.heightAnchor.constraint(equalToConstant: 100)
        ])
    }
    
    private func setupTableView() {
        tableView.register(PaymentPlanTableViewCell.self)
        tableView.register(PaymentMessageTableViewCell.self)
        tableView.dataSource = self
        tableView.delegate = self
    }
    
    @objc private func skipBarButtonTapped() {
        presenter?.skipPaymentPlan()
    }
    
    private func setupCarouselView(with carousels: [String]) {
        
        if !carousels.isEmpty {
            let carousel = CarouselView(frame: carouselRect, config: CarouselConfig.default, carouselImages: carousels)
            tableView.tableHeaderView = carousel
        } else {
            tableView.tableHeaderView = nil
        }
    }
    
    private func updateBottomView(with plan: SelectedPlan) {
        var displayPrice: String?
        var strikedThroughPrice: String?
        
        switch plan {
        case .avod(let plan):
            let currency = plan.currency == "INR" ? "₹" : plan.currency
            displayPrice = "\(currency) \(plan.totalPrice == "0.00" ? "0" : plan.totalPrice)"
            if let sPrice = plan.strikeThroughPrice {
                strikedThroughPrice = "\(currency) \(sPrice == "0.00" ? "0" : sPrice)"
            }
            
        case .tvod(let plan), .svod(let plan):
            displayPrice = plan.skProduct.displayPrice
            strikedThroughPrice = plan.skProduct.strikedThroughPrice
        }
        
        priceLbl.text = displayPrice
        if let _strikedThroughPrice = strikedThroughPrice {
            discountLbl.isHidden = false
            let attributeString =  NSMutableAttributedString(string: _strikedThroughPrice)
            attributeString.addAttribute(NSAttributedString.Key.strikethroughStyle, value: 2, range: NSMakeRange(0, attributeString.length))
            discountLbl.attributedText = attributeString
        } else {
            discountLbl.isHidden = true
        }
        priceLbl.sizeToFit()
        discountLbl.sizeToFit()
        showBottomView(true)
    }
    
    private func showBottomView(_ show: Bool, animated: Bool = true) {
        
        UIView.animate(withDuration: animated ? 0.33 : 0.0) {
            if show {
                self.bottomView.transform = CGAffineTransform(translationX: 0, y: self.view.frame.maxY - self.bottomViewHeight - self.bottomSafeInset)
            } else {
                self.bottomView.transform = CGAffineTransform(translationX: 0, y: self.view.frame.maxY)
            }
        }
    }
    
    @objc func continueAction(_ button: UIButton) {
        presenter?.continueAction(isRetry: false)
    }
    
    func getAnalyticsFunnelMeta() -> [AnalyticsProperty : Any] {
        return self.analyticsFunnelMetadata
    }
    
}

extension PaymentPlanViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        
        return viewModel?.numberOfSections() ?? 0
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        
        return viewModel?.numberOfItemsInSection(section) ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        guard let section = viewModel?.section(at: indexPath.section) else {
            fatalError("Module not configured properly")
        }
        
        switch section {
        case .plan(let type):
            let planCell = tableView.dequeueReusableCell(for: indexPath) as PaymentPlanTableViewCell
            planCell.delegate = self
            planCell.dataSource = self
            planCell.configure(type: type)
            return planCell
            
        case .promoCode:
            // TODO: promoCode cell
            let messageCell = tableView.dequeueReusableCell(for: indexPath) as PaymentMessageTableViewCell
            messageCell.configure(with: "title", message: "message")
            return messageCell
            
        case .iTunesMessage(let title, let message):
            let messageCell = tableView.dequeueReusableCell(for: indexPath) as PaymentMessageTableViewCell
            messageCell.configure(with: title, message: message)
            messageCell.delegate = self
            return messageCell
        }
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 14
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        cell.layoutSubviews()
    }
}

extension PaymentPlanViewController: PaymentPlanViewInput {
    
    //MARK: PaymentPlanViewInput
    func showSkipButton() {
        self.navigationItem.rightBarButtonItem = skipBarButton
    }
    
    func refreshUI(with viewModel: PlanViewModel) {
        self.viewModel = viewModel
        //setupCarouselView(with: viewModel.carousel)
        tableView.reloadData()
        if let paymentConfirmation = self.viewModel?.isPaymentConfirmation(), paymentConfirmation  {
            showSkipButton()
            self.navigationItem.setHidesBackButton(true, animated: true)
        }
    }
    
    func showSelectedPlanView(with plan: SelectedPlan) {
        tableView.reloadData()
        updateBottomView(with: plan)
    }
    
    func showPlanError() {
        let errorView = BottomAlertController(type: BottomAlert.SomethingWentWrong(message: Strings.EmptyStateMessage.somethingWentWrong), presentType: .subView(view: self.view))
        errorView.setPrimaryBtnAction {[unowned self] in
            self.presenter?.fetchPlans()
        }
        errorView.show(over: self)
    }
    
    func showPaymentError() {
        
        let errorView = BottomAlertController(type: BottomAlert.PaymentFailed, presentType: .popup)
        errorView.dismissOnTapOutside = true
        errorView.setPrimaryBtnAction {[unowned self] in
            self.presenter?.continueAction(isRetry: true)
        }
        errorView.show(over: self)
    }
    
    func showMoneyDeductedError() {
        // Enhanced message that mentions automatic refresh
        let enhancedMessage = "Payment completed successfully, but there was a technical issue activating your subscription. We're automatically refreshing your account status. Please check back in a moment or restart the app. If the issue persists, contact <EMAIL>"

        let errorView = BottomAlertController(type: BottomAlert.SomethingWentWrong(message: enhancedMessage), presentType: .popup)
        errorView.dismissOnTapOutside = true

        // Add a "Check Status" button that refreshes user data
        errorView.setPrimaryBtnAction { [weak self] in
            // Manually trigger another refresh
            IAPService.shared.refreshUserDataAfterPayment()

            // Show a brief loading indicator
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // Check if user is now subscribed
                if ENCache.shared.isPaidUser {
                    // Success! Dismiss and navigate
                    errorView.dismiss()
                    self?.presenter?.routeUserPostPayment()
                } else {
                    // Still not activated, show snackbar
                    self?.showSnackbar(with: "Still checking... Please try again in a moment.", type: .info)
                }
            }
        }

        errorView.show(over: self)
    }
    
    func showErrorInSnackbar(with message: String) {
        showSnackbar(with: message, type: .failure)
    }
}

extension PaymentPlanViewController: PaymentMessageTableViewCellDelegate {
    
    func privacyPolicyTapped() {
        presenter?.privacyPolicyTapped()
    }
    
    func tNcTapped() {
        presenter?.tNcTapped()
    }
}

extension PaymentPlanViewController: PaymentPlanTableViewCellDelegate, PaymentPlanTableViewCellDataSource {
    
    func paymentPlanSelected(type: SubscriptionType, indexPath: IndexPath) {
        presenter?.select(type: type, indexPath: indexPath)
        tableView.reloadData()
    }
    
    func paymentActionBtnTapped(type: SubscriptionType) {
        presenter?.sectionActionTapped(type: type)
    }
    
    func isPaymentPlanSelected(type: SubscriptionType, indexPath: IndexPath) -> Bool {
        return presenter?.isSelected(type: type, indexPath: indexPath) ?? false
    }
    
}
