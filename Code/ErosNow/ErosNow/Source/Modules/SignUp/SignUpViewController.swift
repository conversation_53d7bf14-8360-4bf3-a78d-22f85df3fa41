//
//  SignUpViewController.swift
//  ErosNow
//
//  Created by <PERSON> on 16/07/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import UIKit
import SkyFloatingLabelTextField
import RxCocoa
import RxSwift
import SVProgressHUD


final class SignUpViewController: UIViewController, SignUpViewInput {

    //MARK: Navigation Properties
    @IBOutlet weak var textFieldMobileOrEmail: FloatingPlaceholderTextField!
    @IBOutlet weak var socialView: UIView!
    @IBOutlet weak var continueButton: GradientButton!
    
    
    //MARK: class properties
    let disposeBag = DisposeBag()
    
    //MARK: View properties
    var socialChildVC: SocialLoginViewController!
    
    //MARK: Properties
    var presenter: SignUpViewOutput!
    
    //MARK: Initialization
    override init(nibName nibNameOrNil: String? = nil, bundle nibBundleOrNil: Bundle? = nil) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //MARK: ViewController Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        Validator.startObservingCountryCode()
        setupViews()
        themeViews()
        presenter.viewDidLoad(vc: self)
    }
    
    deinit {
            Validator.stopObservingCountryCode()
        }    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        themeNavigationBar()
        AnalyticsManager.shared.trackScreen(withName: .signupVC)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.textFieldDidChanged()
    }
    
    fileprivate func themeNavigationBar() {
        hideNavigationBar(false)
        themeNavigationBar(style: .transparent)
        setNeedsStatusBarAppearanceUpdate()
    }
    
    //MARK: Private Methods
    
    //Configure Views and subviews
    private func setupViews() {
        
        self.resignKeyboardOnTouchBegan()
        
        textFieldMobileOrEmail.autocorrectionType = .no
        textFieldMobileOrEmail.floatingDelegate = self
        textFieldMobileOrEmail.placeholder = Strings.SignUpVC.emailOrMobilePlaceholder
        textFieldMobileOrEmail.title = Strings.LoginVC.mobileNoOrEmail
        if #available(iOS 11.0, *) {
            textFieldMobileOrEmail.textContentType = .username
        }
        
        //Adding bar button items skip and help
        let skipBarButton = UIBarButtonItem(title: Strings.skip, style: .plain, target: self, action: #selector(skipButtonTapped(_:)))
        skipBarButton.setTitleTextAttributesForAllStates()

        let helpBarButton = UIBarButtonItem(title: Strings.help, style: .plain, target: self, action: #selector(helpButtonTapped(_:)))
        helpBarButton.setTitleTextAttributesForAllStates()
        
        let space = UIBarButtonItem(barButtonSystemItem: .fixedSpace, target: nil, action: nil)
        space.width = 20

        navigationItem.rightBarButtonItems = [skipBarButton, space, helpBarButton]

        //adding social view controller as child in local view named socialView
        addChild(socialChildVC)
        socialView.addSubview(socialChildVC.view)
        socialChildVC.didMove(toParent: self)
        
        continueButton.isEnabled = false
    }
    
    //Apply Theming for views here
    private func themeViews() {
        view.backgroundColor = .background
        //setting up material textfield
        self.textFieldMobileOrEmail.setupTextField()
    }
    
    //MARK: SignUpViewInput
    func selectedCountryCode(callingCode: String) {
        self.textFieldMobileOrEmail.callingCode = callingCode
    }
    
    func verificationFailure(with errorDescription: String, signupType: SignupType) {
        AnalyticsManager.shared.track(event: .signUpFail, metadata: [AnalyticsProperty.registrationType: signupType.rawValue, .screenName : ENScreenName.signupVC.rawValue, .categoryName : AnalyticsCategory.authentication.rawValue, .error: errorDescription])
        
        DispatchQueue.main.async {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.showSnackbar(with: errorDescription)
            }
        }
    }
    
    func verificationSuccess(_ signupType: SignupType) {
        AnalyticsManager.shared.track(event: .signUpSuccess, metadata: [AnalyticsProperty.registrationType: signupType.rawValue, .screenName : ENScreenName.signupVC.rawValue, .categoryName : AnalyticsCategory.authentication.rawValue])
    }
    
    //MARK: Button Actions
    @IBAction func continueButtonTapped(_ sender: Any) {
        self.textFieldMobileOrEmail.resignFirstResponder()
        
        // Show loading indicator
        SVProgressHUD.show(withStatus: "Verifying...")
        
        // First verify reCAPTCHA
//        RecaptchaHandler.shared.verify { [weak self] in
//            guard let self = self else { return }
            
            // reCAPTCHA verification successful, proceed with user verification
            let callingCode = self.textFieldMobileOrEmail.isLeftViewActive ? self.textFieldMobileOrEmail.callingCode : nil
            let mobile = self.textFieldMobileOrEmail.isLeftViewActive ? self.textFieldMobileOrEmail.text : nil
            let email = !self.textFieldMobileOrEmail.isLeftViewActive ? self.textFieldMobileOrEmail.text : nil
            
            let type = self.textFieldMobileOrEmail.isLeftViewActive ? "mobile" : "email"
            AnalyticsManager.shared.track(event: .signUpInitiate, metadata: [AnalyticsProperty.registrationType: type, .screenName : ENScreenName.signupVC.rawValue, .categoryName : AnalyticsCategory.authentication.rawValue])
            
            self.presenter.verifyUserViaMobileOrEmail(mobile: mobile, callingCode: callingCode, email: email)
//        }
    }
    
    // delete your account button tapped
    @IBAction func termsButtonTapped(_ sender: Any) {
        let storyboard = UIStoryboard(name: "ENChangeSubscription", bundle: nil)
        if let subscriptionDetailsVC = storyboard.instantiateViewController(withIdentifier: "ENChangeSubscription") as? ENChangeSubscription {
            navigationController?.navigationBar.setBackgroundImage(nil, for: .default)
            navigationController?.navigationBar.shadowImage = nil
            navigationController?.navigationBar.isTranslucent = false
            navigationController?.navigationBar.barTintColor = .background
            
            navigationController?.pushViewController(subscriptionDetailsVC, animated: true)
        }
    }
    
    @objc func skipButtonTapped(_ sender: Any){
        AnalyticsManager.shared.track(event: .skipClicked, metadata: [ .screenName : ENScreenName.signupVC.rawValue, .categoryName : AnalyticsCategory.skip.rawValue])
        self.presenter.skipLogin()
    }
    
    @objc func helpButtonTapped(_ sender: Any){
        AnalyticsManager.shared.track(event: .helpClicked, metadata: [ .screenName : ENScreenName.signupVC.rawValue, .categoryName : AnalyticsCategory.support.rawValue])
       self.presenter.help()
    }
    
    //MARK: Textfield RX Binding
    private func textFieldDidChanged(){
        //text
       textFieldMobileOrEmail.rx.text
            .orEmpty
            .debounce(.milliseconds(100), scheduler: MainScheduler.instance)
            .distinctUntilChanged()
            .filter { !$0.isEmpty }
            .flatMapLatest { [weak self] (text: String) -> Observable<Bool> in
                return Observable
                    .just(self?.presenter.validateInputText(text.trimmed))
                    .map { $0 != .invalid }
            }
            .subscribe(onNext: { [weak self] (isValid: Bool) in
                guard let self = self else {
                    return
                }
                self.continueButton.isEnabled = isValid
            })
            .disposed(by: disposeBag)
    }
    
    func showAlreadySocialSignup(with type: SignupType, email: String) {
        let signUpInfo = AlreadySignupInfo(signupType: type, userName: "", userSignUpId: email, userImage: nil)
        let signupAlert = AlreadySignUpController(signupInfo: signUpInfo, delegate: self)
        self.present(signupAlert, animated: true, completion: nil)
    }
    
}

///Extension to confrom custom textfield delegate
extension SignUpViewController: FloatingViewDelegate{
    
    func countryCodeViewDidTapped(){
        self.presenter.openCountryCodeViewController()
    }
    
}

extension SignUpViewController: AlreadySignUpControllerDelegate {
    
    func signupWithFacebook(alreadySignupWith userEmail: String) {
//        self.socialChildVC.fbButton.sendActions(for: .touchUpInside)
    }
    
    func signupWithGoogle(alreadySignupWith userEmail: String) {
        self.socialChildVC.googleButton.sendActions(for: .touchUpInside)
    }
    
    func signupWithDifferentAccount(alreadySignupWith type: SignupType, userSignUpId: String) {
        
    }
    
}
